@echo off
echo ========================================
echo Script para juntar arquivos .md em Word
echo ========================================
echo.

REM Verifica se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python não encontrado no sistema.
    echo Por favor, instale o Python primeiro.
    pause
    exit /b 1
)

echo Python encontrado.
echo.

REM Verifica se a biblioteca python-docx está instalada
echo Verificando dependências...
python -c "import docx" >nul 2>&1
if errorlevel 1 (
    echo Biblioteca python-docx não encontrada.
    echo Instalando python-docx...
    pip install python-docx
    if errorlevel 1 (
        echo ERRO: Falha ao instalar python-docx
        pause
        exit /b 1
    )
    echo python-docx instalado com sucesso.
) else (
    echo python-docx já está instalado.
)

echo.
echo Executando o script...
echo.

REM Executa o script Python
python juntar_md_para_word.py

echo.
echo ========================================
echo Script finalizado!
echo ========================================
pause
