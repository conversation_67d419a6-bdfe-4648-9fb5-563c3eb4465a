# Script para Juntar Arquivos .md em Word

Este script junta todos os arquivos .md da pasta atual em um único documento Word, removendo automaticamente todas as URLs do sites.google.com.

## Arquivos Criados

1. **juntar_md_para_word.py** - Script principal em Python
2. **executar_script.bat** - Script batch para execução fácil no Windows
3. **README_INSTRUCOES.md** - Este arquivo com instruções

## Como Usar

### Opção 1: Execução Automática (Recomendada)
1. Clique duas vezes no arquivo `executar_script.bat`
2. O script irá:
   - Verificar se o Python está instalado
   - Instalar a biblioteca `python-docx` se necessário
   - Executar o processamento dos arquivos
   - Criar o documento Word final

### Opção 2: Execução Manual
1. Abra o prompt de comando na pasta dos arquivos
2. Instale a dependência (se necessário):
   ```
   pip install python-docx
   ```
3. Execute o script:
   ```
   python juntar_md_para_word.py
   ```

## O que o Script Faz

1. **Encontra todos os arquivos .md** na pasta atual
2. **Remove URLs do sites.google.com** de todo o conteúdo
3. **Decodifica nomes dos arquivos** para títulos legíveis
4. **Converte markdown básico** (cabeçalhos #, ##, ###, etc.)
5. **Cria um documento Word** com:
   - Título principal centralizado
   - Cada arquivo .md como uma seção separada
   - Quebras de página entre seções
   - Formatação adequada de cabeçalhos

## Arquivo de Saída

O documento Word será salvo como: **Centralizacao_Documentacao_Completa.docx**

## Requisitos

- Python 3.6 ou superior
- Biblioteca `python-docx` (instalada automaticamente pelo script .bat)

## Funcionalidades Especiais

- **Limpeza de URLs**: Remove completamente todas as referências ao sites.google.com
- **Decodificação de nomes**: Converte nomes de arquivos URL-encoded para títulos legíveis
- **Formatação inteligente**: Preserva a estrutura de cabeçalhos do markdown
- **Tratamento de erros**: Continua processando mesmo se um arquivo apresentar problemas

## Exemplo de Conversão de Títulos

- `sites.google.com_view_centralizacao_temas_venda-e-compra.md` → "Temas Venda E Compra"
- `sites.google.com_view_centralizacao_%C3%ADndice.md` → "Índice"
- `sites.google.com_view_centralizacao_in%C3%ADcio.md` → "Início"

## Solução de Problemas

Se encontrar erros:

1. **Python não encontrado**: Instale o Python do site oficial (python.org)
2. **Erro de permissão**: Execute como administrador
3. **Erro de codificação**: Verifique se os arquivos .md estão em UTF-8
4. **Biblioteca não instala**: Tente `pip install --user python-docx`

## Suporte

O script processa automaticamente todos os arquivos .md encontrados e cria um log do processamento no console.
