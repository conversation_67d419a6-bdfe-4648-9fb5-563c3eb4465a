#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para juntar arquivos .md em um documento Word
Remove URLs do sites.google.com do conteúdo
"""

import os
import glob
import re
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from urllib.parse import unquote

def limpar_urls_google_sites(texto):
    """Remove URLs do sites.google.com do texto"""
    # Remove URLs completas do sites.google.com
    texto = re.sub(r'https?://sites\.google\.com[^\s\)]*', '', texto)
    
    # Remove referências a sites.google.com que possam ter sobrado
    texto = re.sub(r'sites\.google\.com[^\s\)]*', '', texto)
    
    # Remove linhas vazias extras que podem ter sobrado
    texto = re.sub(r'\n\s*\n\s*\n', '\n\n', texto)
    
    return texto.strip()

def decodificar_nome_arquivo(nome_arquivo):
    """Decodifica o nome do arquivo URL-encoded para um título legível"""
    # Remove a extensão .md
    nome_sem_extensao = nome_arquivo.replace('.md', '')
    
    # Remove o prefixo sites.google.com_view_centralizacao
    nome_limpo = nome_sem_extensao.replace('sites.google.com_view_centralizacao', '')
    
    # Remove underscores do início
    nome_limpo = nome_limpo.lstrip('_')
    
    # Decodifica caracteres URL-encoded
    nome_decodificado = unquote(nome_limpo)
    
    # Substitui underscores por espaços e capitaliza
    titulo = nome_decodificado.replace('_', ' ').replace('-', ' ')
    titulo = titulo.title()
    
    # Se o título estiver vazio, usar um nome padrão
    if not titulo.strip():
        titulo = "Página Principal"
    
    return titulo

def processar_arquivos_md():
    """Processa todos os arquivos .md e cria um documento Word"""
    
    # Encontra todos os arquivos .md
    arquivos_md = glob.glob("*.md")
    arquivos_md.sort()  # Ordena alfabeticamente
    
    if not arquivos_md:
        print("Nenhum arquivo .md encontrado na pasta atual.")
        return
    
    print(f"Encontrados {len(arquivos_md)} arquivos .md")
    
    # Cria um novo documento Word
    doc = Document()
    
    # Adiciona título principal
    titulo_principal = doc.add_heading('Centralização - Documentação Completa', 0)
    titulo_principal.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    
    # Adiciona uma quebra de página
    doc.add_page_break()
    
    # Processa cada arquivo
    for i, arquivo in enumerate(arquivos_md):
        print(f"Processando: {arquivo}")
        
        try:
            # Lê o conteúdo do arquivo
            with open(arquivo, 'r', encoding='utf-8') as f:
                conteudo = f.read()
            
            # Remove URLs do sites.google.com
            conteudo_limpo = limpar_urls_google_sites(conteudo)
            
            # Gera título baseado no nome do arquivo
            titulo_secao = decodificar_nome_arquivo(arquivo)
            
            # Adiciona título da seção
            doc.add_heading(titulo_secao, level=1)
            
            # Adiciona o conteúdo
            if conteudo_limpo.strip():
                # Divide o conteúdo em linhas e processa
                linhas = conteudo_limpo.split('\n')
                
                for linha in linhas:
                    linha = linha.strip()
                    if linha:
                        # Verifica se é um cabeçalho markdown
                        if linha.startswith('# '):
                            doc.add_heading(linha[2:], level=2)
                        elif linha.startswith('## '):
                            doc.add_heading(linha[3:], level=3)
                        elif linha.startswith('### '):
                            doc.add_heading(linha[4:], level=4)
                        elif linha.startswith('#### '):
                            doc.add_heading(linha[5:], level=5)
                        else:
                            # Adiciona como parágrafo normal
                            paragrafo = doc.add_paragraph(linha)
            else:
                doc.add_paragraph("(Conteúdo vazio ou apenas URLs removidas)")
            
            # Adiciona quebra de página entre seções (exceto na última)
            if i < len(arquivos_md) - 1:
                doc.add_page_break()
                
        except Exception as e:
            print(f"Erro ao processar {arquivo}: {e}")
            doc.add_heading(f"Erro ao processar: {arquivo}", level=1)
            doc.add_paragraph(f"Erro: {str(e)}")
    
    # Salva o documento
    nome_arquivo_saida = "Centralizacao_Documentacao_Completa.docx"
    doc.save(nome_arquivo_saida)
    
    print(f"\nDocumento criado com sucesso: {nome_arquivo_saida}")
    print(f"Total de arquivos processados: {len(arquivos_md)}")

if __name__ == "__main__":
    # Verifica se a biblioteca python-docx está instalada
    try:
        from docx import Document
        print("Biblioteca python-docx encontrada.")
    except ImportError:
        print("ERRO: Biblioteca python-docx não encontrada.")
        print("Para instalar, execute: pip install python-docx")
        exit(1)
    
    print("Iniciando processamento dos arquivos .md...")
    processar_arquivos_md()
    print("Processamento concluído!")
